<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Image Converter - Convert Images Between Formats Online</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Image Converter - Convert Images Between Formats Online",
        "description": "Convert images between formats (JPG, PNG, WebP, GIF, BMP) instantly. Free online tool with batch conversion, quality control, and instant downloads.",
        "url": "https://www.webtoolskit.org/p/image-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Image Converter",
            "applicationCategory": "MultimediaApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        }
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I convert an image from one format to another?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert an image from one format to another, simply upload your image using our free online converter, select your desired output format (JPG, PNG, WebP, GIF, or BMP), adjust any quality settings if needed, and click the 'Convert' button. Once conversion is complete, download your newly formatted image with a single click."
          }
        },
        {
          "@type": "Question",
          "name": "What is the best free online image converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The best free online image converter should offer multiple format options, maintain high quality, process files quickly, protect your privacy, and be easy to use. Our Image Converter tool meets all these criteria by supporting all major formats (JPG, PNG, WebP, GIF, BMP), providing quality control options, processing files locally in your browser for privacy, and featuring a clean, intuitive interface."
          }
        },
        {
          "@type": "Question",
          "name": "Can I convert JPG to PNG without losing quality?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, you can convert JPG to PNG without losing additional quality. However, it's important to understand that JPG is a lossy format, so any quality loss that occurred when the image was initially saved as JPG cannot be recovered. Our converter ensures no further quality degradation occurs during the conversion process, and PNG's lossless compression will preserve the image exactly as it appears in the JPG."
          }
        },
        {
          "@type": "Question",
          "name": "How do I convert WebP images to JPEG or PNG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert WebP images to JPEG or PNG, upload your WebP file to our Image Converter tool, select either 'JPG' or 'PNG' as your output format, adjust quality settings if desired, and click 'Convert'. WebP is a modern format that many older programs don't support, so converting to JPG or PNG ensures better compatibility with all software and devices."
          }
        },
        {
          "@type": "Question",
          "name": "Does image conversion affect quality?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Image conversion can affect quality depending on the formats involved. Converting from a lossless format (like PNG) to a lossy one (like JPG) will reduce quality. However, converting from lossy to lossless won't improve quality but prevents further degradation. Our converter offers quality control options to help you balance file size and image quality when converting to lossy formats."
          }
        }
      ]
    }
    </script>

    <style>
        /* Core styles */
        .image-converter-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }
        .image-converter-container * { box-sizing: border-box; }
        
        /* Title and description */
        .image-converter-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .image-converter-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }
        
        /* Dropzone */
        .image-converter-dropzone {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            text-align: center;
            margin-bottom: var(--spacing-xl);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            position: relative;
            cursor: pointer;
        }
        .image-converter-dropzone:hover, .image-converter-dropzone.active {
            border-color: var(--primary-color);
            background-color: rgba(0, 71, 171, 0.05);
        }
        .image-converter-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-md);
        }
        .image-converter-dropzone-text {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-sm);
        }
        .image-converter-dropzone-subtext {
            color: var(--text-color-light);
            font-size: 0.95rem;
        }
        .image-converter-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }
        
        /* Options */
        .image-converter-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }
        .image-converter-option-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }
        .image-converter-select {
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-md);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            background-color: var(--card-bg);
            color: var(--text-color);
            transition: var(--transition-base);
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .image-converter-range {
            width: 100%;
            -webkit-appearance: none;
            height: 6px;
            border-radius: 3px;
            background: var(--border-color);
            outline: none;
            margin-top: var(--spacing-sm);
        }
        .image-converter-range::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
            transition: var(--transition-base);
        }
        .image-converter-range::-moz-range-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
            transition: var(--transition-base);
            border: none;
        }
        .image-converter-quality-value {
            display: inline-block;
            margin-left: var(--spacing-sm);
            font-weight: 600;
            color: var(--primary-color);
        }
        
        /* Buttons */
        .image-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }
        .image-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }
        .image-converter-btn:hover { transform: translateY(-2px); }
        .image-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        .image-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }
        .image-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }
        .image-converter-btn-success {
            background-color: #10b981;
            color: white;
        }
        
        /* Preview and Results */
        .image-converter-preview, .image-converter-result {
            display: none;
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }
        .image-converter-preview.show, .image-converter-result.show { display: block; }
        .image-converter-preview-title, .image-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }
        .image-converter-preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-lg);
            justify-content: center;
        }
        .image-converter-preview-item {
            flex: 1;
            min-width: 200px;
            max-width: 300px;
            text-align: center;
        }
        .image-converter-preview-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
            object-fit: contain;
            background-color: var(--card-bg);
        }
        .image-converter-result-item {
            display: flex;
            align-items: center;
            padding: var(--spacing-md);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-md);
            margin-bottom: var(--spacing-md);
            border: 1px solid var(--border-color);
        }
        
        /* Preview and result item styles */
        .image-converter-preview-label {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-sm);
            font-size: 0.9rem;
            text-align: center;
            word-break: break-word;
        }
        
        .image-converter-preview-info {
            font-size: 0.85rem;
            color: var(--text-color-light);
            margin-top: var(--spacing-sm);
            text-align: center;
        }
        
        .image-converter-result-info {
            flex: 1;
        }
        
        .image-converter-result-name {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 0.375rem;
            word-break: break-word;
        }
        
        .image-converter-result-meta {
            font-size: 0.875rem;
            color: var(--text-color-light);
        }
        
        .image-converter-result-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .image-converter-result-btn {
            padding: 0.375rem 0.75rem;
            border: none;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }
        
        .image-converter-result-btn:hover {
            background-color: var(--border-color);
            transform: translateY(-1px);
        }
        
        .image-converter-result-btn-download {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .image-converter-result-btn-download:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }
        
        /* Notification */
        .image-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }
        .image-converter-notification.show { transform: translateX(0); }
        
        /* Content sections */
        .seo-content, .image-converter-features, .image-converter-related-tools {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        
        /* Features */
        .image-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }
        .image-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }
        .image-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            margin-bottom: 0.3em;
        }
        .image-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        /* Related tools */
        .image-converter-related-tools-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-xl);
            font-size: 1.5rem;
            font-weight: 700;
            text-align: center;
        }
        .image-converter-related-tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-md);
            margin-top: var(--spacing-lg);
            justify-items: center;
            width: 100%;
            max-width: 100%;
            overflow: hidden;
            box-sizing: border-box;
            padding: 0 var(--spacing-sm);
        }
        .image-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        .image-converter-related-tool-item {
            text-align: center;
            text-decoration: none;
            color: inherit;
            transition: var(--transition-base);
            padding: var(--spacing-md);
            border-radius: var(--border-radius-lg);
            display: block;
            width: 100%;
            max-width: 100%;
            min-width: 0;
            box-sizing: border-box;
            overflow: hidden;
        }
        .image-converter-related-tool-name {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-color);
            margin-top: var(--spacing-sm);
            line-height: 1.3;
        }
        
        /* Tool icon colors */
        a[href*="image-resizer"] .image-converter-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }
        a[href*="image-cropper"] .image-converter-related-tool-icon { background: linear-gradient(145deg, #4F46E5, #4338CA); }
        a[href*="image-to-base64"] .image-converter-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }
        
        /* Enhanced hover effects */
        .image-converter-related-tool-item:hover .image-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="image-resizer"]:hover .image-converter-related-tool-icon { background: linear-gradient(145deg, #F87171, #EF4444); }
        a[href*="image-cropper"]:hover .image-converter-related-tool-icon { background: linear-gradient(145deg, #6366F1, #4F46E5); }
        a[href*="image-to-base64"]:hover .image-converter-related-tool-icon { background: linear-gradient(145deg, #F472B6, #EC4899); }
        
        .image-converter-related-tool-item { box-shadow: none; border: none; }
        .image-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .image-converter-related-tool-item:hover .image-converter-related-tool-name { color: var(--primary-color); }
        
        /* Responsive */
        @media (max-width: 768px) {
            .image-converter-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .image-converter-title { font-size: 1.875rem; }
            .image-converter-buttons { flex-direction: column; }
            .image-converter-btn { flex: none; }
            .image-converter-options { grid-template-columns: 1fr; }
            .image-converter-preview-container { flex-direction: column; }
            .image-converter-preview-item { max-width: 100%; }
            .image-converter-result-item { flex-direction: column; align-items: flex-start; gap: var(--spacing-sm); }
            .image-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .image-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .image-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .image-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .image-converter-dropzone { padding: var(--spacing-lg) var(--spacing-md); }
            .image-converter-icon { font-size: 2.5rem; }
            .image-converter-dropzone-text { font-size: 1.125rem; }
            .image-converter-dropzone-subtext { font-size: 0.875rem; }
            .image-converter-result-item { padding: var(--spacing-sm); }
            .image-converter-result-btn { padding: 0.25rem 0.5rem; font-size: 0.8125rem; }
            .image-converter-related-tools-grid { 
                grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); 
                gap: var(--spacing-sm); 
                padding: 0 0.5rem;
            }
            .image-converter-related-tool-item { 
                padding: var(--spacing-sm); 
                max-width: 100%; 
                min-width: 0;
            }
            .image-converter-related-tool-icon { 
                width: 48px; 
                height: 48px; 
                font-size: 1.5rem; 
                border-radius: 12px; 
            }
            .image-converter-related-tool-name { 
                font-size: 0.75rem; 
                word-wrap: break-word;
            }
        }

        @media (max-width: 600px) { 
            .image-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } 
        }

        @media (max-width: 360px) {
            .image-converter-related-tools-grid { 
                grid-template-columns: repeat(2, 1fr); 
                gap: 0.5rem; 
                padding: 0 0.25rem;
            }
            .image-converter-related-tool-item { 
                padding: 0.5rem 0.25rem; 
            }
            .image-converter-related-tool-icon { 
                width: 40px; 
                height: 40px; 
                font-size: 1.25rem; 
                border-radius: 10px; 
            }
            .image-converter-related-tool-name { 
                font-size: 0.7rem; 
                line-height: 1.2;
            }
        }

        /* Dark theme and accessibility */
        [data-theme="dark"] .image-converter-select:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        [data-theme="dark"] .image-converter-range:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .image-converter-select:focus, .image-converter-btn:focus, .image-converter-range:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .image-converter-result-item::selection { background-color: var(--primary-color); color: white; }
    </style>
</head>
<body>
    <div class="image-converter-container">
        <h1 class="image-converter-title">Image Converter</h1>
        <p class="image-converter-description">
            Convert images between multiple formats with ease. Support for JPG, PNG, WebP, GIF, and BMP with quality control and batch processing.
        </p>
        
        <div class="image-converter-dropzone" id="imageConverterDropzone">
            <div class="image-converter-icon">
                <i class="fas fa-cloud-upload-alt"></i>
            </div>
            <h3 class="image-converter-dropzone-text">Drop your images here or click to browse</h3>
            <p class="image-converter-dropzone-subtext">Supports JPG, PNG, WebP, GIF, BMP (Max 10MB per file)</p>
            <input type="file" id="imageConverterInput" class="image-converter-input" accept="image/*" multiple>
        </div>

        <div class="image-converter-options">
            <div class="image-converter-option-group">
                <label for="imageConverterFormat" class="image-converter-option-label">Output Format:</label>
                <select id="imageConverterFormat" class="image-converter-select">
                    <option value="jpeg">JPG</option>
                    <option value="png">PNG</option>
                    <option value="webp">WebP</option>
                    <option value="gif">GIF</option>
                    <option value="bmp">BMP</option>
                </select>
            </div>
            <div class="image-converter-option-group">
                <label for="imageConverterQuality" class="image-converter-option-label">
                    Quality: <span id="imageConverterQualityValue" class="image-converter-quality-value">90%</span>
                </label>
                <input type="range" id="imageConverterQuality" class="image-converter-range" min="10" max="100" value="90">
            </div>
        </div>

        <div class="image-converter-buttons">
            <button class="image-converter-btn image-converter-btn-primary" id="imageConverterConvertBtn">
                Convert Images
            </button>
            <button class="image-converter-btn image-converter-btn-secondary" id="imageConverterClearBtn">
                Clear All
            </button>
            <button class="image-converter-btn image-converter-btn-success" id="imageConverterDownloadAllBtn">
                Download All
            </button>
        </div>

        <div class="image-converter-preview" id="imageConverterPreview">
            <h3 class="image-converter-preview-title">Image Preview:</h3>
            <div class="image-converter-preview-container" id="imageConverterPreviewContainer">
                <!-- Preview images will be added here -->
            </div>
        </div>

        <div class="image-converter-result" id="imageConverterResult">
            <h3 class="image-converter-result-title">Converted Images:</h3>
            <div id="imageConverterResultList">
                <!-- Result items will be added here -->
            </div>
        </div>

        <div class="image-converter-related-tools">
            <h3 class="image-converter-related-tools-title">Related Tools</h3>
            <div class="image-converter-related-tools-grid">
                <a href="/p/image-resizer.html" class="image-converter-related-tool-item" rel="noopener">
                    <div class="image-converter-related-tool-icon">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </div>
                    <div class="image-converter-related-tool-name">Image Resizer</div>
                </a>

                <a href="/p/image-cropper.html" class="image-converter-related-tool-item" rel="noopener">
                    <div class="image-converter-related-tool-icon">
                        <i class="fas fa-crop-alt"></i>
                    </div>
                    <div class="image-converter-related-tool-name">Image Cropper</div>
                </a>

                <a href="/p/image-to-base64.html" class="image-converter-related-tool-item" rel="noopener">
                    <div class="image-converter-related-tool-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="image-converter-related-tool-name">Image to Base64</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Convert Images Between Formats with Our Free Online Tool</h2>
            <p>Our <strong>Image Converter</strong> transforms your images between popular formats with just a few clicks. Whether you need JPG to PNG for transparency or WebP to JPG for compatibility, this browser-based tool handles it all without requiring any software installation.</p>
            
            <h3>Understanding Image Formats</h3>
            <ul>
                <li><strong>JPG/JPEG:</strong> Best for photos. Uses lossy compression for smaller files with some quality loss.</li>
                <li><strong>PNG:</strong> Ideal for transparency and sharp details. Lossless compression preserves quality but creates larger files.</li>
                <li><strong>WebP:</strong> Modern format with superior compression. Supports both lossy and lossless compression with transparency.</li>
                <li><strong>GIF:</strong> Perfect for simple animations. Supports transparency but has limited colors.</li>
                <li><strong>BMP:</strong> Uncompressed format that preserves all details but creates very large files.</li>
            </ul>
            
            <h3>How to Use Our Image Converter</h3>
            <ol>
                <li><strong>Upload:</strong> Drag and drop files or click to browse your device.</li>
                <li><strong>Select Format:</strong> Choose your desired output format (JPG, PNG, WebP, GIF, BMP).</li>
                <li><strong>Adjust Quality:</strong> Use the slider for JPG and WebP formats.</li>
                <li><strong>Convert:</strong> Click the "Convert Images" button.</li>
                <li><strong>Download:</strong> Get your converted images individually or all at once.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Image Converter</h3>
            
            <h4>Can I convert JPG to PNG without losing quality?</h4>
            <p>Yes, converting JPG to PNG won't cause additional quality loss. However, any quality already lost in the original JPG can't be recovered. PNG's lossless compression will preserve the image exactly as it appears.</p>
            
            <h4>Does image conversion affect quality?</h4>
            <p>Converting from lossless (PNG) to lossy (JPG) formats will reduce quality. Converting from lossy to lossless won't improve quality but prevents further degradation. Our quality slider helps you balance file size and image quality for lossy formats.</p>
            
            <h4>Is my data secure during conversion?</h4>
            <p>Absolutely. All processing happens locally in your browser—your images never leave your device, ensuring complete privacy and security.</p>
        </div>

        <div class="image-converter-features">
            <h3 class="image-converter-features-title">Key Features:</h3>
            <ul class="image-converter-features-list">
                <li class="image-converter-features-item">Convert between JPG, PNG, WebP, GIF, and BMP</li>
                <li class="image-converter-features-item">Batch processing for multiple images</li>
                <li class="image-converter-features-item">Adjustable quality settings</li>
                <li class="image-converter-features-item">Preview before downloading</li>
                <li class="image-converter-features-item">Fast browser-based conversion</li>
                <li class="image-converter-features-item">No file size limits</li>
                <li class="image-converter-features-item">100% private - files never leave your device</li>
            </ul>
        </div>
    </div>

    <div class="image-converter-notification" id="imageConverterNotification">
        ✓ Image converted successfully!
    </div>

    <script>
        // Image Converter Tool
        (function() {
            'use strict';
            
            // DOM elements
            const el = {
                dropzone: document.getElementById('imageConverterDropzone'),
                input: document.getElementById('imageConverterInput'),
                format: document.getElementById('imageConverterFormat'),
                quality: document.getElementById('imageConverterQuality'),
                qualityValue: document.getElementById('imageConverterQualityValue'),
                convertBtn: document.getElementById('imageConverterConvertBtn'),
                clearBtn: document.getElementById('imageConverterClearBtn'),
                downloadAllBtn: document.getElementById('imageConverterDownloadAllBtn'),
                preview: document.getElementById('imageConverterPreview'),
                previewContainer: document.getElementById('imageConverterPreviewContainer'),
                result: document.getElementById('imageConverterResult'),
                resultList: document.getElementById('imageConverterResultList'),
                notification: document.getElementById('imageConverterNotification')
            };
            
            // State
            const state = {
                files: [],
                convertedImages: [],
                isConverting: false
            };
            
            // Initialize
            function init() {
                // Set up event listeners
                el.input.addEventListener('change', e => handleFiles(e.target.files));
                el.convertBtn.addEventListener('click', convertImages);
                el.clearBtn.addEventListener('click', clearAll);
                el.downloadAllBtn.addEventListener('click', downloadAllImages);
                el.quality.addEventListener('input', () => el.qualityValue.textContent = `${el.quality.value}%`);
                el.format.addEventListener('change', () => {
                    const format = el.format.value;
                    el.quality.parentElement.style.display = (format === 'jpeg' || format === 'webp') ? 'block' : 'none';
                });
                
                // Set up drag and drop
                el.dropzone.addEventListener('dragover', e => {
                    e.preventDefault();
                    el.dropzone.classList.add('active');
                });
                
                el.dropzone.addEventListener('dragleave', () => el.dropzone.classList.remove('active'));
                
                el.dropzone.addEventListener('drop', e => {
                    e.preventDefault();
                    el.dropzone.classList.remove('active');
                    if (e.dataTransfer.files.length) handleFiles(e.dataTransfer.files);
                });
                
                el.dropzone.addEventListener('click', () => el.input.click());
                
                // Theme compatibility
                const theme = localStorage.getItem('theme') || 
                    (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }
            }
            
            // Handle files
            function handleFiles(fileList) {
                const imageFiles = Array.from(fileList).filter(file => 
                    file.type.startsWith('image/') && file.size <= 10 * 1024 * 1024
                );
                
                if (!imageFiles.length) {
                    showNotification('Please select valid image files (max 10MB each)', 'error');
                    return;
                }
                
                state.files = [...state.files, ...imageFiles];
                updatePreview();
                el.preview.classList.add('show');
            }
            
            // Update preview
            function updatePreview() {
                el.previewContainer.innerHTML = '';
                
                state.files.forEach((file, index) => {
                    const reader = new FileReader();
                    
                    reader.onload = e => {
                        const item = document.createElement('div');
                        item.className = 'image-converter-preview-item';
                        
                        const label = document.createElement('div');
                        label.className = 'image-converter-preview-label';
                        label.textContent = file.name;
                        
                        const img = document.createElement('img');
                        img.className = 'image-converter-preview-image';
                        img.src = e.target.result;
                        img.alt = file.name;
                        
                        const info = document.createElement('div');
                        info.className = 'image-converter-preview-info';
                        info.textContent = `${formatSize(file.size)} - ${getExtension(file.name).toUpperCase()}`;
                        
                        const btn = document.createElement('button');
                        btn.className = 'image-converter-result-btn';
                        btn.textContent = 'Remove';
                        btn.addEventListener('click', () => removeFile(index));
                        
                        item.append(label, img, info, btn);
                        el.previewContainer.appendChild(item);
                    };
                    
                    reader.readAsDataURL(file);
                });
            }
            
            // Remove file
            function removeFile(index) {
                state.files.splice(index, 1);
                
                if (state.files.length === 0) {
                    el.preview.classList.remove('show');
                } else {
                    updatePreview();
                }
            }
            
            // Convert images
            function convertImages() {
                if (!state.files.length) {
                    showNotification('Please add images to convert', 'error');
                    return;
                }
                
                if (state.isConverting) return;
                
                state.isConverting = true;
                state.convertedImages = [];
                el.convertBtn.textContent = 'Converting...';
                el.resultList.innerHTML = '';
                
                const format = el.format.value;
                const quality = parseInt(el.quality.value) / 100;
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                let processed = 0;
                
                state.files.forEach(file => {
                    const reader = new FileReader();
                    
                    reader.onload = e => {
                        const img = new Image();
                        
                        img.onload = () => {
                            // Set canvas dimensions
                            canvas.width = img.width;
                            canvas.height = img.height;
                            
                            // Draw image
                            ctx.clearRect(0, 0, canvas.width, canvas.height);
                            ctx.drawImage(img, 0, 0);
                            
                            // Get format info
                            const mimeType = `image/${format}`;
                            
                            // Convert
                            const dataURL = (format === 'jpeg' || format === 'webp') 
                                ? canvas.toDataURL(mimeType, quality)
                                : canvas.toDataURL(mimeType);
                            
                            // Create filename
                            const baseName = file.name.substring(0, file.name.lastIndexOf('.')) || file.name;
                            const newFileName = `${baseName}.${format}`;
                            
                            // Store result
                            state.convertedImages.push({
                                name: newFileName,
                                dataURL: dataURL,
                                type: mimeType,
                                originalSize: file.size,
                                convertedSize: calculateDataURLSize(dataURL)
                            });
                            
                            processed++;
                            
                            // Check if all processed
                            if (processed === state.files.length) {
                                displayResults();
                                state.isConverting = false;
                                el.convertBtn.textContent = 'Convert Images';
                                showNotification('Conversion complete!');
                            }
                        };
                        
                        img.src = e.target.result;
                    };
                    
                    reader.readAsDataURL(file);
                });
            }
            
            // Display results
            function displayResults() {
                el.resultList.innerHTML = '';
                
                state.convertedImages.forEach((image, index) => {
                    const item = document.createElement('div');
                    item.className = 'image-converter-result-item';
                    
                    const info = document.createElement('div');
                    info.className = 'image-converter-result-info';
                    
                    const name = document.createElement('div');
                    name.className = 'image-converter-result-name';
                    name.textContent = image.name;
                    
                    const meta = document.createElement('div');
                    meta.className = 'image-converter-result-meta';
                    
                    const originalSize = formatSize(image.originalSize);
                    const convertedSize = formatSize(image.convertedSize);
                    const sizeDiff = image.convertedSize - image.originalSize;
                    const sizeDiffText = sizeDiff < 0 
                        ? `Saved ${formatSize(Math.abs(sizeDiff))}` 
                        : `Increased by ${formatSize(sizeDiff)}`;
                    
                    meta.textContent = `${convertedSize} (${sizeDiffText})`;
                    
                    const actions = document.createElement('div');
                    actions.className = 'image-converter-result-actions';
                    
                    const downloadBtn = document.createElement('button');
                    downloadBtn.className = 'image-converter-result-btn image-converter-result-btn-download';
                    downloadBtn.textContent = 'Download';
                    downloadBtn.addEventListener('click', () => downloadImage(index));
                    
                    info.append(name, meta);
                    actions.appendChild(downloadBtn);
                    item.append(info, actions);
                    
                    el.resultList.appendChild(item);
                });
                
                el.result.classList.add('show');
            }
            
            // Download image
            function downloadImage(index) {
                const image = state.convertedImages[index];
                const link = document.createElement('a');
                link.href = image.dataURL;
                link.download = image.name;
                link.click();
            }
            
            // Download all
            function downloadAllImages() {
                if (!state.convertedImages.length) {
                    showNotification('No converted images to download', 'error');
                    return;
                }
                
                state.convertedImages.forEach((image, i) => {
                    setTimeout(() => downloadImage(i), i * 100);
                });
                
                showNotification('Downloading all images...');
            }
            
            // Clear all
            function clearAll() {
                state.files = [];
                state.convertedImages = [];
                el.input.value = '';
                el.preview.classList.remove('show');
                el.result.classList.remove('show');
                el.previewContainer.innerHTML = '';
                el.resultList.innerHTML = '';
            }
            
            // Show notification
            function showNotification(message, type = 'success') {
                el.notification.textContent = message;
                el.notification.style.backgroundColor = type === 'success' ? '#10b981' : '#ef4444';
                el.notification.classList.add('show');
                setTimeout(() => el.notification.classList.remove('show'), 3000);
            }
            
            // Helper: Format file size
            function formatSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
            
            // Helper: Get file extension
            function getExtension(filename) {
                return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
            }
            
            // Helper: Calculate data URL size
            function calculateDataURLSize(dataURL) {
                const base64 = dataURL.split(',')[1];
                return atob(base64).length;
            }
            
            // Initialize
            document.addEventListener('DOMContentLoaded', init);
        })();
    </script>
</body>
</html>